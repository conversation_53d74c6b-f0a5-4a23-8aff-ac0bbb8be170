#!/usr/bin/env python3
"""
Test script to verify LangSmith agent-specific tracing is working.
This script creates a simple test run to verify that individual agent traces appear in LangSmith.
"""

import os
import asyncio
from langsmith import Client as LangSmithClient

# Set up LangSmith environment
os.environ["LANGSMITH_API_KEY"] = "***************************************************"
os.environ["LANGSMITH_PROJECT"] = "rathid-gdk"
os.environ["LANGSMITH_ENDPOINT"] = "https://api.smith.langchain.com"
os.environ["LANGSMITH_TRACING"] = "true"

async def test_agent_specific_tracing():
    """Test that agent-specific traces are created in LangSmith."""
    
    print("🧪 Testing LangSmith Agent-Specific Tracing...")
    
    # Initialize LangSmith client
    client = LangSmithClient()
    
    # Test 1: Goal Disambiguation Agent Trace
    print("\n1️⃣ Creating Goal Disambiguation Agent trace...")
    
    goal_disambiguation_inputs = {
        "agent_name": "Goal_Disambiguation_Agent",
        "goal": "Test goal for tracing verification",
        "current_status": "GOAL_DISAMBIGUATION",
        "workflow_id": "test-workflow-123",
        "workflow_type": "software_development",
        "test_run": True
    }
    
    goal_run = client.create_run(
        name="Goal_Disambiguation_Agent",
        run_type="chain",
        inputs=goal_disambiguation_inputs,
        extra={
            "metadata": {
                "agent_type": "goal_disambiguation",
                "workflow_id": "test-workflow-123",
                "workflow_type": "software_development",
                "component": "GoalDisambiguationComponent",
                "test": True
            }
        }
    )
    
    # Simulate some processing
    await asyncio.sleep(0.1)
    
    goal_outputs = {
        "agent_name": "Goal_Disambiguation_Agent",
        "result_type": "dict",
        "conversation_updates": ["goal_disambiguation"],
        "status_change": "PLANNING",
        "clarity_decision": {
            "decision_type": "goal_clear",
            "clarity_score": 0.9,
            "clarity_grade": "CLEAR"
        },
        "success": True,
        "test_run": True
    }
    
    client.update_run(
        run_id=goal_run.id,
        outputs=goal_outputs,
        end_time=None
    )
    
    print(f"   ✅ Goal Disambiguation Agent trace created: {goal_run.id}")
    
    print(f"\n🎉 Test trace created successfully!")
    print(f"📊 View traces in LangSmith: https://smith.langchain.com/projects/rathid-gdk")
    print(f"\n🔍 Look for trace name: Goal_Disambiguation_Agent")
    print(f"\n💡 The trace should appear as a separate run with agent-specific metadata and I/O")

if __name__ == "__main__":
    asyncio.run(test_agent_specific_tracing())
