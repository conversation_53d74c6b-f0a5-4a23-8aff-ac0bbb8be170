#!/usr/bin/env python3
"""
Test script to verify chat workflow tracing is working.
"""

import sys
import os
import asyncio

# Add the gitlab-ai-gateway directory to the Python path
sys.path.insert(0, '/Users/<USER>/Developer/gitlab/gdk/gitlab-ai-gateway')

async def test_chat_workflow_tracing():
    """Test that the chat workflow can be imported and has tracing methods."""
    
    print("🔍 Testing Chat Workflow Tracing...")
    
    try:
        # Test import
        from duo_workflow_service.workflows.chat.workflow import ChatWorkflow
        print("✅ Chat workflow imported successfully")
        
        # Check if tracing methods exist
        if hasattr(ChatWorkflow, '_create_traced_chat_agent_run'):
            print("✅ _create_traced_chat_agent_run method exists")
        else:
            print("❌ _create_traced_chat_agent_run method missing")
            
        if hasattr(ChatWorkflow, '_create_traced_chat_tools_run'):
            print("✅ _create_traced_chat_tools_run method exists")
        else:
            print("❌ _create_traced_chat_tools_run method missing")
            
        print("✅ Chat workflow tracing implementation verified!")
        
    except Exception as e:
        print(f"❌ Error testing chat workflow: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_chat_workflow_tracing())
