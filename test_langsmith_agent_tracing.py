#!/usr/bin/env python3
"""
Test script to verify LangSmith agent-specific tracing is working.
This script creates a simple test run to verify that individual agent traces appear in LangSmith.
"""

import os
import asyncio
from langsmith import Client as LangSmithClient

# Set up LangSmith environment
os.environ["LANGSMITH_API_KEY"] = "***************************************************"
os.environ["LANGSMITH_PROJECT"] = "rathid-gdk"
os.environ["LANGSMITH_ENDPOINT"] = "https://api.smith.langchain.com"
os.environ["LANGSMITH_TRACING"] = "true"

async def test_agent_specific_tracing():
    """Test that agent-specific traces are created in LangSmith."""
    
    print("🧪 Testing LangSmith Agent-Specific Tracing...")
    
    # Initialize LangSmith client
    client = LangSmithClient()
    
    # Test 1: Goal Disambiguation Agent Trace
    print("\n1️⃣ Creating Goal Disambiguation Agent trace...")
    
    goal_disambiguation_inputs = {
        "agent_name": "Goal_Disambiguation_Agent",
        "goal": "Test goal for tracing verification",
        "current_status": "GOAL_DISAMBIGUATION",
        "workflow_id": "test-workflow-123",
        "workflow_type": "software_development",
        "test_run": True
    }
    
    goal_run = client.create_run(
        name="Goal_Disambiguation_Agent",
        run_type="chain",
        inputs=goal_disambiguation_inputs,
        extra={
            "metadata": {
                "agent_type": "goal_disambiguation",
                "workflow_id": "test-workflow-123",
                "workflow_type": "software_development",
                "component": "GoalDisambiguationComponent",
                "test": True
            }
        }
    )
    
    # Simulate some processing
    await asyncio.sleep(0.1)
    
    goal_outputs = {
        "agent_name": "Goal_Disambiguation_Agent",
        "result_type": "dict",
        "conversation_updates": ["goal_disambiguation"],
        "status_change": "PLANNING",
        "clarity_decision": {
            "decision_type": "goal_clear",
            "clarity_score": 0.9,
            "clarity_grade": "CLEAR"
        },
        "success": True,
        "test_run": True
    }
    
    client.update_run(
        run_id=goal_run.id,
        outputs=goal_outputs,
        end_time=None
    )
    
    print(f"   ✅ Goal Disambiguation Agent trace created: {goal_run.id}")
    
    # Test 2: Planning Agent Trace
    print("\n2️⃣ Creating Planning Agent trace...")
    
    planning_inputs = {
        "agent_name": "Planning_Agent",
        "goal": "Test goal for tracing verification",
        "current_status": "PLANNING",
        "workflow_id": "test-workflow-123",
        "workflow_type": "software_development",
        "test_run": True
    }
    
    planning_run = client.create_run(
        name="Planning_Agent",
        run_type="chain",
        inputs=planning_inputs,
        extra={
            "metadata": {
                "agent_type": "planning",
                "workflow_id": "test-workflow-123",
                "workflow_type": "software_development",
                "component": "PlannerComponent",
                "test": True
            }
        }
    )
    
    await asyncio.sleep(0.1)
    
    planning_outputs = {
        "agent_name": "Planning_Agent",
        "result_type": "dict",
        "conversation_updates": ["planner"],
        "status_change": "CONTEXT_GATHERING",
        "plan_created": {
            "total_steps": 3,
            "plan_type": "software_development",
            "estimated_duration": "30 minutes"
        },
        "success": True,
        "test_run": True
    }
    
    client.update_run(
        run_id=planning_run.id,
        outputs=planning_outputs,
        end_time=None
    )
    
    print(f"   ✅ Planning Agent trace created: {planning_run.id}")
    
    # Test 3: Context Gathering Agent Trace
    print("\n3️⃣ Creating Context Gathering Agent trace...")
    
    context_inputs = {
        "agent_name": "Context_Gathering_Agent",
        "goal": "Test goal for tracing verification",
        "current_status": "CONTEXT_GATHERING",
        "workflow_id": "test-workflow-123",
        "workflow_type": "software_development",
        "test_run": True
    }
    
    context_run = client.create_run(
        name="Context_Gathering_Agent",
        run_type="chain",
        inputs=context_inputs,
        extra={
            "metadata": {
                "agent_type": "context_gathering",
                "workflow_id": "test-workflow-123",
                "workflow_type": "software_development",
                "component": "ContextGatheringComponent",
                "test": True
            }
        }
    )
    
    await asyncio.sleep(0.1)
    
    context_outputs = {
        "agent_name": "Context_Gathering_Agent",
        "result_type": "dict",
        "conversation_updates": ["context_builder"],
        "status_change": "EXECUTION",
        "information_collected": {
            "files_read": 5,
            "directories_explored": 2,
            "git_operations": 1,
            "total_context_length": 15000
        },
        "success": True,
        "test_run": True
    }
    
    client.update_run(
        run_id=context_run.id,
        outputs=context_outputs,
        end_time=None
    )
    
    print(f"   ✅ Context Gathering Agent trace created: {context_run.id}")
    
    # Test 4: Execution Agent Trace
    print("\n4️⃣ Creating Execution Agent trace...")
    
    execution_inputs = {
        "agent_name": "Execution_Agent",
        "goal": "Test goal for tracing verification",
        "current_status": "EXECUTION",
        "workflow_id": "test-workflow-123",
        "workflow_type": "software_development",
        "test_run": True
    }
    
    execution_run = client.create_run(
        name="Execution_Agent",
        run_type="chain",
        inputs=execution_inputs,
        extra={
            "metadata": {
                "agent_type": "execution",
                "workflow_id": "test-workflow-123",
                "workflow_type": "software_development",
                "component": "ExecutorComponent",
                "test": True
            }
        }
    )
    
    await asyncio.sleep(0.1)
    
    execution_outputs = {
        "agent_name": "Execution_Agent",
        "result_type": "dict",
        "conversation_updates": ["executor"],
        "status_change": "COMPLETED",
        "tasks_completed": {
            "total_tasks": 3,
            "completed_tasks": 3,
            "failed_tasks": 0,
            "success_rate": 100.0
        },
        "success": True,
        "test_run": True
    }
    
    client.update_run(
        run_id=execution_run.id,
        outputs=execution_outputs,
        end_time=None
    )
    
    print(f"   ✅ Execution Agent trace created: {execution_run.id}")
    
    print(f"\n🎉 All agent traces created successfully!")
    print(f"📊 View traces in LangSmith: https://smith.langchain.com/projects/rathid-gdk")
    print(f"\n🔍 Look for these trace names:")
    print(f"   • Goal_Disambiguation_Agent")
    print(f"   • Planning_Agent") 
    print(f"   • Context_Gathering_Agent")
    print(f"   • Execution_Agent")
    print(f"\n💡 Each trace should appear as a separate run with agent-specific metadata and I/O")

if __name__ == "__main__":
    asyncio.run(test_agent_specific_tracing())
