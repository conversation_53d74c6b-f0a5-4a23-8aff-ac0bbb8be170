#!/usr/bin/env python3
"""
Simple test to trigger a chat workflow and verify tracing.
"""

import grpc
import sys
import os

# Add the gitlab-ai-gateway directory to the Python path
sys.path.insert(0, '/Users/<USER>/Developer/gitlab/gdk/gitlab-ai-gateway')

from contract import contract_pb2, contract_pb2_grpc

def test_chat_workflow():
    """Test chat workflow via gRPC to see if tracing works."""
    
    print("🔍 Testing Chat Workflow via gRPC...")
    
    try:
        # Connect to the gRPC service
        channel = grpc.insecure_channel('localhost:50052')
        stub = contract_pb2_grpc.DuoWorkflowStub(channel)
        
        # Create a simple chat request using ClientEvent
        def generate_chat_events():
            yield contract_pb2.ClientEvent(
                startRequest=contract_pb2.StartWorkflowRequest(
                    clientVersion="1",
                    workflowDefinition="chat",  # Use chat workflow specifically
                    goal="Hello, can you help me understand this project?"
                )
            )

        print("📤 Sending chat workflow request...")

        # Send the request (this returns a stream)
        responses = stub.ExecuteWorkflow(generate_chat_events())

        # Process the first response to get workflow ID
        first_response = next(responses)
        workflow_id = getattr(first_response, 'workflowId', 'unknown')

        print(f"✅ Chat workflow started: {workflow_id}")
        print("🔍 Check LangSmith dashboard for Chat_Agent and Chat_Tools_Executor traces!")
        print("🌐 LangSmith URL: https://smith.langchain.com/projects/rathid-gdk")

        return workflow_id
        
    except Exception as e:
        print(f"❌ Error testing chat workflow: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    workflow_id = test_chat_workflow()
    if workflow_id:
        print(f"\n🎯 Workflow ID: {workflow_id}")
        print("📊 Go to LangSmith to see the agent-specific traces!")
    else:
        print("\n❌ Test failed")
