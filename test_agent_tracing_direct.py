#!/usr/bin/env python3
"""
Direct test of agent tracing to verify <PERSON><PERSON><PERSON> integration is working.
This script directly tests the agent tracing methods to see if they create traces.
"""

import os
import asyncio
import sys
from pathlib import Path

# Add the gitlab-ai-gateway directory to the Python path
sys.path.insert(0, str(Path(__file__).parent / "gitlab-ai-gateway"))

# Set up environment variables
os.environ["LANGCHAIN_TRACING_V2"] = "true"
os.environ["LANGCHAIN_API_KEY"] = "***************************************************"
os.environ["LANGCHAIN_PROJECT"] = "rathid-gdk"
os.environ["LANGSMITH_TRACING"] = "true"
os.environ["LANGSMITH_API_KEY"] = "***************************************************"
os.environ["LANGSMITH_PROJECT"] = "rathid-gdk"
os.environ["DEBUG"] = "true"

from langsmith import Client as LangSmithClient, traceable

async def test_direct_agent_tracing():
    """Test direct agent tracing to verify LangSmith integration."""
    
    print("🧪 Testing Direct Agent Tracing")
    print("=" * 50)
    
    # Initialize LangSmith client
    client = LangSmithClient()
    
    print("1️⃣ Testing LangSmith connection...")
    try:
        info = client.info
        print(f"   ✅ Connected to LangSmith: {info}")
    except Exception as e:
        print(f"   ❌ LangSmith connection failed: {e}")
        return
    
    print("\n2️⃣ Testing @traceable decorator...")
    
    @traceable(
        name="Test_Goal_Disambiguation_Agent",
        run_type="chain",
        metadata={
            "agent_type": "goal_disambiguation",
            "workflow_id": "test-workflow-123",
            "test": True
        }
    )
    async def test_goal_agent(goal: str):
        """Test goal disambiguation agent tracing."""
        print(f"   🔍 Processing goal: {goal}")
        
        # Simulate some processing
        await asyncio.sleep(0.1)
        
        return {
            "agent_name": "Goal_Disambiguation_Agent",
            "goal": goal,
            "clarity_decision": "CLEAR",
            "success": True
        }
    
    @traceable(
        name="Test_Planning_Agent", 
        run_type="chain",
        metadata={
            "agent_type": "planning",
            "workflow_id": "test-workflow-123",
            "test": True
        }
    )
    async def test_planning_agent(goal: str):
        """Test planning agent tracing."""
        print(f"   📋 Creating plan for: {goal}")
        
        await asyncio.sleep(0.1)
        
        return {
            "agent_name": "Planning_Agent",
            "goal": goal,
            "plan_created": True,
            "success": True
        }
    
    # Test each agent
    test_goal = "Create a simple Python function to calculate fibonacci numbers"
    
    print("\n3️⃣ Running traced agent functions...")
    
    try:
        goal_result = await test_goal_agent(test_goal)
        print(f"   ✅ Goal Agent completed: {goal_result['clarity_decision']}")
        
        planning_result = await test_planning_agent(test_goal)
        print(f"   ✅ Planning Agent completed: {planning_result['plan_created']}")
        
    except Exception as e:
        print(f"   ❌ Agent execution failed: {e}")
        return
    
    print("\n🎉 All agent traces created successfully!")
    print(f"📊 View traces in LangSmith: https://smith.langchain.com/projects/rathid-gdk")

if __name__ == "__main__":
    asyncio.run(test_direct_agent_tracing())
