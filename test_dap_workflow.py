#!/usr/bin/env python3
"""
Test script to trigger a DAP workflow and verify agent-specific tracing.
"""

import asyncio
import grpc
import sys
from pathlib import Path

# Add the gitlab-ai-gateway directory to the Python path
sys.path.insert(0, str(Path(__file__).parent / "gitlab-ai-gateway"))

from ********************.protos import ********************_pb2
from ********************.protos import ********************_pb2_grpc

async def test_dap_workflow():
    """Test DAP workflow execution to verify agent tracing."""
    
    print("🚀 Testing DAP Workflow Execution")
    print("=" * 50)
    
    # Connect to the gRPC server
    channel = grpc.aio.insecure_channel('localhost:50052')
    stub = ********************_pb2_grpc.DuoWorkflowServiceStub(channel)
    
    print("1️⃣ Testing gRPC connection...")
    try:
        # Create a simple workflow request
        request = ********************_pb2.ExecuteWorkflowRequest(
            goal="Create a simple Python function to calculate fibonacci numbers",
            workflow_type="software_development",
            workflow_metadata={
                "extended_logging": True,  # Enable agent-level tracing
                "git_url": "https://github.com/test/test-repo.git",
                "git_sha": "abc123"
            }
        )
        
        print("   📝 Created workflow request")
        print(f"   🎯 Goal: {request.goal}")
        print(f"   🔧 Type: {request.workflow_type}")
        print(f"   📊 Extended logging: {request.workflow_metadata.get('extended_logging', False)}")
        
        print("\n2️⃣ Executing workflow...")
        
        # Execute the workflow
        response_stream = stub.ExecuteWorkflow(request)
        
        action_count = 0
        async for response in response_stream:
            action_count += 1
            print(f"   📨 Action {action_count}: {response.action.action_type}")
            
            # Limit the number of actions we process for testing
            if action_count >= 5:
                print("   ⏹️  Stopping after 5 actions for testing")
                break
        
        print(f"\n✅ Workflow executed successfully! Processed {action_count} actions")
        
    except grpc.RpcError as e:
        print(f"   ❌ gRPC error: {e.code()} - {e.details()}")
        return False
    except Exception as e:
        print(f"   ❌ Workflow execution failed: {e}")
        return False
    finally:
        await channel.close()
    
    print("\n🎉 DAP workflow test completed!")
    print(f"📊 Check LangSmith traces at: https://smith.langchain.com/projects/rathid-gdk")
    print(f"\n🔍 You should now see agent-specific traces like:")
    print(f"   • Goal_Disambiguation_Agent")
    print(f"   • Planning_Agent")
    print(f"   • Context_Gathering_Agent")
    print(f"   • Execution_Agent")
    
    return True

if __name__ == "__main__":
    success = asyncio.run(test_dap_workflow())
    if success:
        print("\n✅ Test completed successfully!")
    else:
        print("\n❌ Test failed!")
        sys.exit(1)
