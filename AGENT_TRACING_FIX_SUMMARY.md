# Agent-Level LangSmith Tracing Fix for GitLab DAP

## 🔍 **Problem Identified**

You were expecting to see agent-specific Lang<PERSON><PERSON> traces like:
- `Goal_Disambiguation_Agent`
- `Planning_Agent` 
- `Context_Gathering_Agent`
- `Execution_Agent`

But instead, you were only seeing high-level workflow traces with metadata like:
```json
{
  "thread_id": "17",
  "git_url": "http://127.0.0.1:3000/gitlab-duo/ai-assist.git",
  "workflow_type": "chat",
  "langgraph_node": "agent"
}
```

## 🕵️ **Root Cause Analysis**

The issue was in `duo_workflow_service/workflows/abstract_workflow.py` line 146:

```python
extended_logging = self._workflow_metadata.get("extended_logging", False)
```

**The Problem:**
1. Agent-level tracing was implemented correctly in all DAP components
2. However, it was only enabled when `extended_logging=True` in workflow metadata
3. The default value was `False`, so agent tracing was disabled
4. The `tracing_context(enabled=extended_logging)` wrapper controlled whether agent traces were created

## ✅ **Solution Implemented**

### 1. **Fixed Extended Logging Logic**
Updated `duo_workflow_service/workflows/abstract_workflow.py`:

```python
# Enable extended logging (agent-level tracing) based on DEBUG env var or workflow metadata
# DEBUG=true enables extended logging by default, otherwise respect workflow metadata
debug_enabled = os.environ.get("DEBUG", "false").lower() == "true"
extended_logging = self._workflow_metadata.get("extended_logging", debug_enabled)
```

### 2. **Added Debug Logging**
Added logging to help track when extended logging is enabled:

```python
self.log.info(
    f"Extended logging (agent-level tracing) enabled: {extended_logging}",
    workflow_id=self._workflow_id,
    extended_logging=extended_logging
)
```

### 3. **Created Enable Script**
Created `enable-agent-tracing.py` script that:
- Sets `DEBUG=true` in `duo-workflow-executor/.env`
- Provides instructions for restarting the service
- Explains what traces to expect

## 🚀 **How to Enable Agent Tracing**

### Option 1: Use the Script (Recommended)
```bash
python3 enable-agent-tracing.py
gdk stop duo-workflow-service
gdk start duo-workflow-service
```

### Option 2: Manual Configuration
1. Edit `duo-workflow-executor/.env`:
   ```bash
   DEBUG=true
   ```

2. Restart the service:
   ```bash
   gdk stop duo-workflow-service
   gdk start duo-workflow-service
   ```

## 🔍 **What You Should See Now**

After enabling agent tracing, your LangSmith traces should show:

### **Individual Agent Traces:**
- **`Goal_Disambiguation_Agent`** (Chain type)
- **`Context_Gathering_Agent`** (Chain type)
- **`Planning_Agent`** (Chain type)
- **`Execution_Agent`** (Chain type)

### **Agent Sub-Operations:**
- `Goal_Disambiguation_Ask_Question` (Tool type)
- `Context_Gathering_Tools_Executor` (Tool type)
- `Planning_Tools_Executor` (Tool type)
- `Execution_Tools_Executor` (Tool type)
- `Planning_Supervisor` (Chain type)
- `Execution_Supervisor` (Chain type)

### **Rich Metadata:**
```json
{
  "agent_type": "goal_disambiguation|planning|context_gathering|execution",
  "workflow_id": "unique-workflow-id",
  "workflow_type": "software_development",
  "component": "GoalDisambiguationComponent|PlannerComponent|etc",
  "operation": "specific-operation-name"
}
```

## 🧪 **Testing**

1. **Trigger a DAP workflow** from VS Code or GitLab
2. **Check LangSmith** at https://smith.langchain.com
3. **Look for project**: `rathid-gdk`
4. **Verify agent traces** are now visible with detailed inputs/outputs

## 🔧 **Technical Details**

### **Tracing Architecture:**
- Each DAP agent component has `_create_traced_*_run()` methods
- These methods use `@traceable` decorators and manual LangSmith client calls
- The `tracing_context(enabled=extended_logging)` wrapper controls activation
- When `extended_logging=False`, agent traces are not created

### **Files Modified:**
- `duo_workflow_service/workflows/abstract_workflow.py` - Fixed extended logging logic
- `enable-agent-tracing.py` - Created helper script

### **Environment Variables:**
- `DEBUG=true` - Enables agent-level tracing by default
- `LANGSMITH_TRACING=true` - Enables LangSmith tracing (already configured)
- `LANGSMITH_PROJECT=rathid-gdk` - Your LangSmith project (already configured)

## 🎯 **Expected Results**

You should now see **granular, agent-level observability** in LangSmith instead of just high-level workflow traces. Each agent's decision-making process, tool usage, and data flow will be clearly visible in separate LangSmith runs.

## 🔄 **To Disable Agent Tracing**

Set `DEBUG=false` in `duo-workflow-executor/.env` and restart the service:
```bash
gdk stop duo-workflow-service
gdk start duo-workflow-service
```
