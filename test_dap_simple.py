#!/usr/bin/env python3
"""
Simple test to trigger a DAP workflow using the existing client.
"""

import os
import sys
from pathlib import Path

# Add the gitlab-ai-gateway directory to the Python path
sys.path.insert(0, str(Path(__file__).parent / "gitlab-ai-gateway"))

# Set environment variables for LangSmith
os.environ["LANGCHAIN_TRACING_V2"] = "true"
os.environ["LANGCHAIN_API_KEY"] = "***************************************************"
os.environ["LANGCHAIN_PROJECT"] = "rathid-gdk"
os.environ["LANGSMITH_TRACING"] = "true"
os.environ["LANGSMITH_API_KEY"] = "***************************************************"
os.environ["LANGSMITH_PROJECT"] = "rathid-gdk"
os.environ["DEBUG"] = "true"

def test_dap_simple():
    """Simple test to check if we can import and run DAP components."""
    
    print("🧪 Testing DAP Components Import")
    print("=" * 50)
    
    try:
        print("1️⃣ Testing imports...")
        
        # Test importing the workflow components
        from duo_workflow_service.workflows.software_development.workflow import SoftwareDevelopmentWorkflow
        print("   ✅ SoftwareDevelopmentWorkflow imported")
        
        from duo_workflow_service.components.goal_disambiguation.component import GoalDisambiguationComponent
        print("   ✅ GoalDisambiguationComponent imported")
        
        from duo_workflow_service.components.planner.component import PlannerComponent
        print("   ✅ PlannerComponent imported")
        
        from duo_workflow_service.components.executor.component import ExecutorComponent
        print("   ✅ ExecutorComponent imported")
        
        print("\n2️⃣ Testing LangSmith setup...")
        from duo_workflow_service.tracking.langsmith_setup import setup_langsmith
        setup_langsmith()
        print("   ✅ LangSmith setup completed")
        
        print("\n3️⃣ Testing workflow creation...")
        
        # Create a workflow instance
        workflow = SoftwareDevelopmentWorkflow(
            workflow_id="test-workflow-123",
            workflow_metadata={
                "extended_logging": True,
                "git_url": "https://github.com/test/test-repo.git",
                "git_sha": "abc123"
            }
        )
        print("   ✅ SoftwareDevelopmentWorkflow created")
        
        print(f"   📝 Workflow ID: {workflow._workflow_id}")
        print(f"   🔧 Workflow Type: {workflow._workflow_type}")
        
        print("\n🎉 All components imported and initialized successfully!")
        print(f"📊 LangSmith should be ready to capture traces at: https://smith.langchain.com/projects/rathid-gdk")
        
        return True
        
    except ImportError as e:
        print(f"   ❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

if __name__ == "__main__":
    success = test_dap_simple()
    if success:
        print("\n✅ Test completed successfully!")
        print("\n💡 Next steps:")
        print("   1. Trigger a real DAP workflow from VS Code or GitLab")
        print("   2. Check LangSmith for agent-specific traces")
        print("   3. Look for traces like Goal_Disambiguation_Agent, Planning_Agent, etc.")
    else:
        print("\n❌ Test failed!")
        sys.exit(1)
