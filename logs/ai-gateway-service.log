2025-09-23_04:19:18.26091 [31;1mgitlab-ai-gateway       : [0mrunit control/t: sending TERM to -38361
2025-09-23_04:19:18.26148 [31;1mgitlab-ai-gateway       : [0mrunit control/t: sending TERM to 38361
2025-09-23_08:49:30.35119 [31;1mgitlab-ai-gateway       : [0mrunit control/t: sending TERM to -20989
2025-09-23_08:49:30.35403 [31;1mgitlab-ai-gateway       : [0mrunit control/t: sending TERM to 20989
2025-09-23_09:19:46.83879 [36mgitlab-ai-gateway       : [0mrunit control/t: sending TERM to -61285
2025-09-23_09:19:46.83948 [36mgitlab-ai-gateway       : [0mrunit control/t: sending TERM to 61285
2025-09-23_09:20:09.27947 [36mgitlab-ai-gateway       : [0mrunit control/t: sending TERM to -64697
2025-09-23_09:20:09.28210 [36mgitlab-ai-gateway       : [0mrunit control/t: sending TERM to 64697
2025-09-23_09:22:50.38163 [36mgitlab-ai-gateway       : [0mrunit control/t: sending TERM to -65949
2025-09-23_09:22:50.38620 [36mgitlab-ai-gateway       : [0mrunit control/t: sending TERM to 65949
2025-09-23_09:41:40.91184 [36mgitlab-ai-gateway       : [0mrunit control/t: sending TERM to -76850
2025-09-23_09:41:40.91283 [36mgitlab-ai-gateway       : [0mrunit control/t: sending TERM to 76850
2025-09-24_09:02:15.90493 [36mgitlab-ai-gateway       : [0mrunit control/t: sending TERM to -47614
2025-09-24_09:02:15.91150 [36mgitlab-ai-gateway       : [0mrunit control/t: sending TERM to 47614
2025-09-25_07:39:23.82322 [31;1mgitlab-ai-gateway       : [0mrunit control/t: sending TERM to -5702
2025-09-25_07:39:23.82754 [31;1mgitlab-ai-gateway       : [0mrunit control/t: sending TERM to 5702
2025-09-25_07:39:27.98228 [31;1mgitlab-ai-gateway       : [0mWARNING: Could not flush telemetry events within 3 seconds. Is https://gdk-telemetry.runway.gitlab.net blocked or unreachable?
2025-09-25_09:21:15.49693 [36mgitlab-ai-gateway       : [0mrunit control/t: sending TERM to -81234
2025-09-25_09:21:15.49834 [36mgitlab-ai-gateway       : [0mrunit control/t: sending TERM to 81234
2025-09-25_10:16:55.45697 [36mgitlab-ai-gateway       : [0mrunit control/t: sending TERM to -38230
2025-09-25_10:16:55.45721 [36mgitlab-ai-gateway       : [0mrunit control/t: sending TERM to 38230
2025-09-25_10:16:59.58995 [36mgitlab-ai-gateway       : [0mWARNING: Could not flush telemetry events within 3 seconds. Is https://gdk-telemetry.runway.gitlab.net blocked or unreachable?
2025-09-25_10:47:33.68499 [36mgitlab-ai-gateway       : [0mWARNING:uvicorn.error:WatchFiles detected changes in 'duo_workflow_service/workflows/abstract_workflow.py'. Reloading...
2025-09-25_10:47:41.44856 [36mgitlab-ai-gateway       : [0mrunit control/t: sending TERM to -11231
2025-09-25_10:47:41.44863 [36mgitlab-ai-gateway       : [0mrunit control/t: sending TERM to 11231
2025-09-25_11:09:51.64844 [36mgitlab-ai-gateway       : [0mrunit control/t: sending TERM to -18816
2025-09-25_11:09:51.64970 [36mgitlab-ai-gateway       : [0mrunit control/t: sending TERM to 18816
2025-09-25_11:52:31.95384 [36mgitlab-ai-gateway       : [0mWARNING:uvicorn.error:WatchFiles detected changes in 'duo_workflow_service/workflows/abstract_workflow.py'. Reloading...
2025-09-25_12:07:31.84444 [36mgitlab-ai-gateway       : [0mWARNING:uvicorn.error:WatchFiles detected changes in 'duo_workflow_service/components/goal_disambiguation/component.py'. Reloading...
2025-09-25_12:07:47.31053 [36mgitlab-ai-gateway       : [0mrunit control/t: sending TERM to -96736
2025-09-25_12:07:47.31056 [36mgitlab-ai-gateway       : [0mrunit control/t: sending TERM to 96736
2025-09-25_12:14:43.26655 [36mgitlab-ai-gateway       : [0mWARNING:uvicorn.error:WatchFiles detected changes in 'duo_workflow_service/components/goal_disambiguation/component.py'. Reloading...
2025-09-26_03:59:13.81898 [36mgitlab-ai-gateway       : [0mWARNING:uvicorn.error:WatchFiles detected changes in 'duo_workflow_service/components/goal_disambiguation/component.py'. Reloading...
2025-09-26_03:59:33.89935 [36mgitlab-ai-gateway       : [0mWARNING:uvicorn.error:WatchFiles detected changes in 'duo_workflow_service/components/goal_disambiguation/component.py'. Reloading...
2025-09-26_04:00:06.05438 [36mgitlab-ai-gateway       : [0mWARNING:uvicorn.error:WatchFiles detected changes in 'duo_workflow_service/components/goal_disambiguation/component.py'. Reloading...
2025-09-26_04:01:12.43527 [36mgitlab-ai-gateway       : [0mWARNING:uvicorn.error:WatchFiles detected changes in 'duo_workflow_service/components/goal_disambiguation/component.py'. Reloading...
2025-09-26_04:01:36.73273 [36mgitlab-ai-gateway       : [0mWARNING:uvicorn.error:WatchFiles detected changes in 'duo_workflow_service/components/goal_disambiguation/component.py'. Reloading...
2025-09-26_04:02:03.95667 [36mgitlab-ai-gateway       : [0mWARNING:uvicorn.error:WatchFiles detected changes in 'duo_workflow_service/components/goal_disambiguation/component.py'. Reloading...
2025-09-26_04:02:27.16411 [36mgitlab-ai-gateway       : [0mWARNING:uvicorn.error:WatchFiles detected changes in 'duo_workflow_service/components/planner/component.py'. Reloading...
2025-09-26_04:02:47.12231 [36mgitlab-ai-gateway       : [0mWARNING:uvicorn.error:WatchFiles detected changes in 'duo_workflow_service/components/planner/component.py'. Reloading...
2025-09-26_04:03:16.78776 [36mgitlab-ai-gateway       : [0mWARNING:uvicorn.error:WatchFiles detected changes in 'duo_workflow_service/components/planner/component.py'. Reloading...
2025-09-26_04:03:36.45187 [36mgitlab-ai-gateway       : [0mWARNING:uvicorn.error:WatchFiles detected changes in 'duo_workflow_service/components/planner/component.py'. Reloading...
2025-09-26_04:04:37.77249 [36mgitlab-ai-gateway       : [0mWARNING:uvicorn.error:WatchFiles detected changes in 'duo_workflow_service/components/planner/component.py'. Reloading...
2025-09-26_04:05:29.57592 [36mgitlab-ai-gateway       : [0mWARNING:uvicorn.error:WatchFiles detected changes in 'duo_workflow_service/workflows/software_development/workflow.py'. Reloading...
2025-09-26_04:05:42.60414 [36mgitlab-ai-gateway       : [0mWARNING:uvicorn.error:WatchFiles detected changes in 'duo_workflow_service/workflows/software_development/workflow.py'. Reloading...
2025-09-26_04:06:17.23331 [36mgitlab-ai-gateway       : [0mWARNING:uvicorn.error:WatchFiles detected changes in 'duo_workflow_service/workflows/software_development/workflow.py'. Reloading...
2025-09-26_04:07:02.16078 [36mgitlab-ai-gateway       : [0mWARNING:uvicorn.error:WatchFiles detected changes in 'duo_workflow_service/workflows/software_development/workflow.py'. Reloading...
2025-09-26_04:07:49.83642 [36mgitlab-ai-gateway       : [0mWARNING:uvicorn.error:WatchFiles detected changes in 'duo_workflow_service/workflows/software_development/workflow.py'. Reloading...
2025-09-26_04:08:12.43426 [36mgitlab-ai-gateway       : [0mWARNING:uvicorn.error:WatchFiles detected changes in 'duo_workflow_service/components/executor/component.py'. Reloading...
2025-09-26_04:08:50.40170 [36mgitlab-ai-gateway       : [0mWARNING:uvicorn.error:WatchFiles detected changes in 'duo_workflow_service/components/executor/component.py'. Reloading...
2025-09-26_04:09:10.92880 [36mgitlab-ai-gateway       : [0mWARNING:uvicorn.error:WatchFiles detected changes in 'duo_workflow_service/components/executor/component.py'. Reloading...
2025-09-26_04:10:06.99762 [36mgitlab-ai-gateway       : [0mWARNING:uvicorn.error:WatchFiles detected changes in 'duo_workflow_service/components/executor/component.py'. Reloading...
2025-09-26_04:10:49.91636 [36mgitlab-ai-gateway       : [0mWARNING:uvicorn.error:WatchFiles detected changes in 'duo_workflow_service/components/executor/component.py'. Reloading...
2025-09-26_04:11:11.22766 [36mgitlab-ai-gateway       : [0mWARNING:uvicorn.error:WatchFiles detected changes in 'duo_workflow_service/components/executor/component.py'. Reloading...
2025-09-26_04:14:05.75361 [36mgitlab-ai-gateway       : [0mrunit control/t: sending TERM to -19471
2025-09-26_04:14:05.75388 [36mgitlab-ai-gateway       : [0mrunit control/t: sending TERM to 19471
2025-09-26_04:21:28.76921 [36mgitlab-ai-gateway       : [0mrunit control/t: sending TERM to -41211
2025-09-26_04:21:28.76975 [36mgitlab-ai-gateway       : [0mrunit control/t: sending TERM to 41211
2025-09-26_04:23:32.79410 [36mgitlab-ai-gateway       : [0mrunit control/t: sending TERM to -68853
2025-09-26_04:23:32.79471 [36mgitlab-ai-gateway       : [0mrunit control/t: sending TERM to 68853
2025-09-26_05:05:24.03101 [36mgitlab-ai-gateway       : [0mrunit control/t: sending TERM to -78018
2025-09-26_05:05:24.03151 [36mgitlab-ai-gateway       : [0mrunit control/t: sending TERM to 78018
2025-09-26_05:07:09.00655 [36mgitlab-ai-gateway       : [0mrunit control/t: sending TERM to -44682
2025-09-26_05:07:09.00691 [36mgitlab-ai-gateway       : [0mrunit control/t: sending TERM to 44682
2025-09-26_05:54:17.99852 [32mgitlab-ai-gateway       : [0mrunit control/t: sending TERM to -52956
2025-09-26_05:54:17.99918 [32mgitlab-ai-gateway       : [0mrunit control/t: sending TERM to 52956
2025-09-26_05:59:27.96763 [32mgitlab-ai-gateway       : [0mrunit control/t: sending TERM to -32207
2025-09-26_05:59:27.96775 [32mgitlab-ai-gateway       : [0mrunit control/t: sending TERM to 32207
2025-09-26_06:01:04.51029 [32mgitlab-ai-gateway       : [0mWARNING:uvicorn.error:WatchFiles detected changes in 'test-langsmith-tracing.py'. Reloading...
2025-09-26_06:02:52.91187 [32mgitlab-ai-gateway       : [0mrunit control/t: sending TERM to -51307
2025-09-26_06:02:52.91190 [32mgitlab-ai-gateway       : [0mrunit control/t: sending TERM to 51307
2025-09-26_06:08:59.74837 [32mgitlab-ai-gateway       : [0mWARNING:uvicorn.error:WatchFiles detected changes in 'test-langsmith-tracing.py'. Reloading...
2025-09-26_06:11:45.83132 [32mgitlab-ai-gateway       : [0mWARNING:uvicorn.error:WatchFiles detected changes in 'duo_workflow_service/server.py'. Reloading...
2025-09-26_06:11:57.85336 [32mgitlab-ai-gateway       : [0mWARNING:uvicorn.error:WatchFiles detected changes in 'duo_workflow_service/server.py'. Reloading...
2025-09-26_06:12:35.04068 [32mgitlab-ai-gateway       : [0mWARNING:uvicorn.error:WatchFiles detected changes in 'duo_workflow_service/tracking/__init__.py'. Reloading...
2025-09-26_06:15:14.86361 [32mgitlab-ai-gateway       : [0mWARNING:uvicorn.error:WatchFiles detected changes in 'duo_workflow_service/tracking/__init__.py'. Reloading...
2025-09-26_06:15:25.50342 [32mgitlab-ai-gateway       : [0mWARNING:uvicorn.error:WatchFiles detected changes in 'duo_workflow_service/server.py'. Reloading...
2025-09-26_06:15:38.85796 [32mgitlab-ai-gateway       : [0mWARNING:uvicorn.error:WatchFiles detected changes in 'duo_workflow_service/server.py'. Reloading...
2025-09-26_06:18:57.75504 [32mgitlab-ai-gateway       : [0mWARNING:uvicorn.error:WatchFiles detected changes in 'duo_workflow_service/server.py'. Reloading...
2025-09-26_06:25:13.41717 [32mgitlab-ai-gateway       : [0mWARNING:uvicorn.error:WatchFiles detected changes in 'duo_workflow_service/tracking/langsmith_setup.py'. Reloading...
2025-09-26_06:26:06.29881 [32mgitlab-ai-gateway       : [0mWARNING:uvicorn.error:WatchFiles detected changes in 'duo_workflow_service/tracking/langsmith_setup.py'. Reloading...
2025-09-26_07:24:18.36734 [32mgitlab-ai-gateway       : [0mWARNING:uvicorn.error:WatchFiles detected changes in 'test_langsmith_tracing.py'. Reloading...
2025-09-26_07:24:32.57290 [32mgitlab-ai-gateway       : [0mWARNING:uvicorn.error:WatchFiles detected changes in 'test_langsmith_tracing.py'. Reloading...
2025-09-26_07:24:45.21923 [32mgitlab-ai-gateway       : [0mWARNING:uvicorn.error:WatchFiles detected changes in 'test_langsmith_tracing.py'. Reloading...
2025-09-26_07:30:04.62336 [32mgitlab-ai-gateway       : [0mrunit control/t: sending TERM to -64200
2025-09-26_07:30:04.62341 [32mgitlab-ai-gateway       : [0mrunit control/t: sending TERM to 64200
2025-09-26_08:01:11.73885 [32mgitlab-ai-gateway       : [0mWARNING:uvicorn.error:WatchFiles detected changes in 'duo_workflow_service/tracking/langsmith_setup.py'. Reloading...
2025-09-26_08:01:25.08547 [32mgitlab-ai-gateway       : [0mWARNING:uvicorn.error:WatchFiles detected changes in 'duo_workflow_service/tracking/langsmith_setup.py'. Reloading...
2025-09-26_08:01:38.38491 [32mgitlab-ai-gateway       : [0mWARNING:uvicorn.error:WatchFiles detected changes in 'duo_workflow_service/tracking/langsmith_setup.py'. Reloading...
2025-09-26_08:01:52.96513 [32mgitlab-ai-gateway       : [0mWARNING:uvicorn.error:WatchFiles detected changes in 'duo_workflow_service/server.py'. Reloading...
2025-09-26_08:02:42.71534 [32mgitlab-ai-gateway       : [0mWARNING:uvicorn.error:WatchFiles detected changes in 'test_langsmith_tracing.py'. Reloading...
2025-09-26_08:41:51.34235 [32mgitlab-ai-gateway       : [0mWARNING:uvicorn.error:WatchFiles detected changes in 'duo_workflow_service/tracking/langsmith_setup.py', 'duo_workflow_service/server.py', 'test_langsmith_tracing.py'. Reloading...
2025-09-26_08:42:02.05898 [32mgitlab-ai-gateway       : [0mWARNING:uvicorn.error:WatchFiles detected changes in 'test_langsmith_tracing.py'. Reloading...
2025-09-26_08:42:07.01527 [32mgitlab-ai-gateway       : [0mrunit control/t: sending TERM to -91166
2025-09-26_08:42:07.01531 [32mgitlab-ai-gateway       : [0mrunit control/t: sending TERM to 91166
2025-09-26_12:59:21.43640 [32mgitlab-ai-gateway       : [0mrunit control/t: sending TERM to -47312
2025-09-26_12:59:21.43701 [32mgitlab-ai-gateway       : [0mrunit control/t: sending TERM to 47312
2025-09-26_14:54:11.97436 [32mgitlab-ai-gateway       : [0mrunit control/t: sending TERM to -3688
2025-09-26_14:54:11.97440 [32mgitlab-ai-gateway       : [0mrunit control/t: sending TERM to 3688
47312
2025-09-26_14:54:11.97436 [32mgitlab-ai-gateway       : [0mrunit control/t: sending TERM to -3688
2025-09-26_14:54:11.97440 [32mgitlab-ai-gateway       : [0mrunit control/t: sending TERM to 3688
.
2025-09-26_08:42:07.01527 [32mgitlab-ai-gateway       : [0mrunit control/t: sending TERM to -91166
2025-09-26_08:42:07.01531 [32mgitlab-ai-gateway       : [0mrunit control/t: sending TERM to 91166
2025-09-26_12:59:21.43640 [32mgitlab-ai-gateway       : [0mrunit control/t: sending TERM to -47312
2025-09-26_12:59:21.43701 [32mgitlab-ai-gateway       : [0mrunit control/t: sending TERM to 47312
2025-09-26_14:54:11.97436 [32mgitlab-ai-gateway       : [0mrunit control/t: sending TERM to -3688
2025-09-26_14:54:11.97440 [32mgitlab-ai-gateway       : [0mrunit control/t: sending TERM to 3688
